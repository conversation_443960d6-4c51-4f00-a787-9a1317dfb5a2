 {% extends "app/campaign/campaign-base.html" %}
 
 {% block title %}Campaign Dashboard{% endblock %}
 
 
 {% block cmp-base-content %}     

        <!-- Status & Filters -->
        <div class="flex items-center justify-between mt-8">
            <div class="flex items-center gap-3">
                <span class="text-sm font-medium text-gray-500">Status:</span>
                <div class="badge badge-neutral text-white font-semibold text-xs py-3">Draft</div>
                <span class="text-sm font-bold text-gray-800">0%</span>
                <progress class="progress progress-primary w-40 h-1.5" value="0" max="100"></progress>
            </div>
            <div class="flex items-center gap-3">
                <button class="btn bg-white border border-base-300 h-10 min-h-10 rounded-lg px-4 text-sm">
                    <i class="fa-solid fa-share-nodes mr-2 text-gray-500"></i>
                    Share
                </button>
                 <div class="dropdown dropdown-end">
                    <button tabindex="0" role="button" class="btn bg-white border border-base-300 h-10 min-h-10 px-3 flex items-center gap-2 rounded-lg">
                        <span class="text-sm font-medium text-gray-700">Last 4 weeks</span>
                        <i class="fa-solid fa-chevron-down text-xs text-gray-500"></i>
                    </button>
                </div>
                <button class="btn bg-white border border-base-300 btn-square h-10 min-h-10 w-10">
                    <i class="fa-solid fa-gear text-gray-500"></i>
                </button>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-6 mt-6">
            <!-- Card 1 -->
            <div class="bg-white border border-gray-200 rounded-xl p-5 shadow-sm">
                <div class="flex justify-between items-center text-gray-400 text-sm font-medium"><span>Sequence started</span><i class="fa-solid fa-circle-info"></i></div>
                <div class="mt-4 text-4xl font-bold text-gray-800">-</div>
            </div>
            <!-- Card 2 -->
            <div class="bg-white border border-gray-200 rounded-xl p-5 shadow-sm">
                <div class="flex justify-between items-center text-gray-400 text-sm font-medium"><span>Open rate</span><i class="fa-solid fa-circle-info"></i></div>
                <div class="mt-4 text-4xl font-bold text-gray-800">0 <span class="text-gray-300 font-light mx-1">|</span> <span class="text-2xl font-semibold">-</span></div>
            </div>
            <!-- Card 3 -->
            <div class="bg-white border border-gray-200 rounded-xl p-5 shadow-sm">
                <div class="flex justify-between items-center text-gray-400 text-sm font-medium"><span>Click rate</span><i class="fa-solid fa-circle-info"></i></div>
                <div class="mt-4 text-4xl font-bold text-gray-800">0 <span class="text-gray-300 font-light mx-1">|</span> <span class="text-2xl font-semibold">-</span></div>
            </div>
            <!-- Card 4 -->
            <div class="bg-white border border-gray-200 rounded-xl p-5 shadow-sm">
                <div class="flex justify-between items-center text-gray-400 text-sm font-medium"><span>Opportunities</span><i class="fa-solid fa-circle-info"></i></div>
                <div class="mt-4 text-4xl font-bold text-gray-800">0 <span class="text-gray-300 font-light mx-1">|</span> <span class="text-2xl font-semibold text-gray-500">$0</span></div>
            </div>
            <!-- Card 5 -->
            <div class="bg-white border border-gray-200 rounded-xl p-5 shadow-sm">
                <div class="flex justify-between items-center text-gray-400 text-sm font-medium"><span>Conversions</span><i class="fa-solid fa-circle-info"></i></div>
                <div class="mt-4 text-4xl font-bold text-gray-800">0 <span class="text-gray-300 font-light mx-1">|</span> <span class="text-2xl font-semibold text-gray-500">$0</span></div>
            </div>
        </div>
        
        <!-- Empty Chart Placeholder -->
        <div class="mt-6 bg-white border border-gray-200 rounded-xl p-12 flex items-center justify-center">
            <p class="text-gray-500 text-sm">No data available for specified time</p>
        </div>

        <!-- Step Analytics & Activity -->
        <div class="mt-6 bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
            <div role="tablist" class="tabs tabs-boxed bg-gray-100 w-max">
                <a role="tab" class="tab tab-active bg-white text-primary shadow-sm">Step Analytics</a> 
                <a role="tab" class="tab">Activity</a>
            </div>
            <div class="mt-6">
                <p class="text-sm text-gray-600">👋 Step analytics will appear here once the campaign is published</p>
            </div>
        </div>





{% endblock %}        