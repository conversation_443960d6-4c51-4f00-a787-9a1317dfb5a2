 {% extends "app/campaign/campaign-base.html" %}
 
 {% block title %}Campaign Leads{% endblock %}
 
 
 
 {% block cmp-base-content %}     
     
     
     
     
       
  
  
  
  


        <!-- Toolbar -->
        <div class="flex items-center justify-between mt-8">
            <div class="flex items-center gap-4">
                <label class="input input-bordered flex items-center gap-2 h-10 w-64 bg-white"><i class="fa-solid fa-magnifying-glass text-gray-400"></i><input type="text" class="grow text-sm" placeholder="Search..." /></label>
                <div class="flex items-center gap-4 text-gray-500 text-sm p-2 rounded-lg bg-gray-100">
                    <div class="flex items-center gap-1.5"><i class="fa-solid fa-users"></i><span class="font-semibold">{{ total_leads }}</span></div>
                    <div class="flex items-center gap-1.5"><i class="fa-solid fa-eye"></i><span class="font-semibold">3</span></div>
                    <div class="flex items-center gap-1.5"><i class="fa-solid fa-paper-plane"></i><span class="font-semibold">2</span></div>
                    <div class="flex items-center gap-1.5"><i class="fa-solid fa-reply"></i><span class="font-semibold">1</span></div>
                    <div class="flex items-center gap-1.5"><i class="fa-regular fa-handshake"></i><span class="font-semibold">0</span></div>
                    <div class="flex items-center gap-1.5"><i class="fa-solid fa-circle-check"></i><span class="font-semibold">1</span></div>
                </div>
            </div>
            <div class="flex items-center gap-3">
                <button class="btn bg-white border border-base-300 h-10 min-h-10 rounded-lg px-4 text-sm"><i class="fa-solid fa-filter mr-2 text-gray-500"></i>Filters</button>
                <button class="btn btn-primary btn-outline btn-square h-10 w-10 min-h-10"><i class="fa-solid fa-wand-magic-sparkles"></i></button>
                <button class="btn btn-primary h-10 min-h-10 rounded-lg px-4 text-sm">Add Leads</button>
            </div>
        </div>

        <!-- Table Container -->
        <div class="mt-4 flex-grow overflow-x-auto rounded-lg border border-gray-200 bg-white">
            <table class="table-auto w-full custom-table">
                <thead>
                    <tr>
                        <th class="w-12 text-center"><input type="checkbox" class="checkbox checkbox-sm rounded" /></th>
                        <th class="w-16"></th> <!-- Index number column -->
                        <th class="text-left">Lead</th>
                        <th class="text-left">Company</th>
                        <th class="text-left">Status</th>
                        <th class="text-left">Links</th>    
                        <th class="text-left">Converted at</th>
                        <th class="text-left">Created at</th>
                    </tr>
                </thead>
                <tbody>
                    {% for lead in leads %}
                    <tr class="hover:bg-gray-50">
                        <td class="text-center">
                            <input type="checkbox" class="checkbox checkbox-sm rounded" />
                        </td>
                        <td class="text-gray-500 text-center">{{ lead.id }}</td>
                        <td class="font-medium text-gray-700">
                            <div class="flex flex-col">
                                <span>{{ lead.first_name }} {{ lead.last_name }}</span>
                                <span class="text-sm text-gray-500">{{ lead.email }}</span>
                            </div>
                        </td>
                        <td class="text-gray-600">{{ lead.company }}</td>
                        <td>
                            <div class="badge {{ lead.status_class }} gap-2 font-medium py-3">
                                <i class="{{ lead.status_icon }}"></i>
                                {{ lead.status }}
                            </div>
                        </td>
                        <td class="text-gray-600">
                            {% if lead.links_count > 0 %}
                                <span class="badge badge-outline">{{ lead.links_count }} links</span>
                            {% else %}
                                <span class="text-gray-400">No links</span>
                            {% endif %}
                        </td>
                        <td class="text-gray-600">
                            {% if lead.converted_at %}
                                {{ lead.converted_at }}
                            {% else %}
                                <span class="text-gray-400">-</span>
                            {% endif %}
                        </td>
                        <td class="text-gray-600">{{ lead.created_at }}</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="8" class="text-center py-8 text-gray-500">
                            <i class="fa-solid fa-users text-2xl mb-2"></i>
                            <p>No leads found for this campaign</p>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        <!-- Footer -->
        <div class="py-6 text-center">
            <hr class="mb-4">
            <div class="flex items-center justify-center gap-4">
                <span class="text-sm text-gray-400">Searching</span>
                <button class="btn btn-sm bg-white border border-gray-300" disabled>No more to load</button>
            </div>
        </div>







      {% endblock %}  