 {% extends "app/campaign/campaign-base.html" %}
 
 {% block title %}Campaign Leads{% endblock %}
 
 
 
 {% block cmp-base-content %}     
     
     
     
     
       
  
  
  
  


        <!-- Toolbar -->
        <div class="flex items-center justify-between mt-8">
            <div class="flex items-center gap-4">
                <label class="input input-bordered flex items-center gap-2 h-10 w-64 bg-white">
                    <i class="fa-solid fa-magnifying-glass text-gray-400"></i>
                    <input type="text"
                           id="search-input"
                           class="grow text-sm"
                           placeholder="Search leads..."
                           hx-trigger="keyup changed delay:300ms"
                           hx-post="#"
                           hx-target="#leads-table-body"
                           hx-include="#status-filter" />
                </label>
                <div id="stats-container" class="flex items-center gap-4 text-gray-500 text-sm p-2 rounded-lg bg-gray-100">
                    <div class="flex items-center gap-1.5" title="Total Leads">
                        <i class="fa-solid fa-users"></i>
                        <span class="font-semibold" id="stat-total">{{ stats.total_leads }}</span>
                    </div>
                    <div class="flex items-center gap-1.5" title="Viewed">
                        <i class="fa-solid fa-eye"></i>
                        <span class="font-semibold" id="stat-viewed">{{ stats.viewed }}</span>
                    </div>
                    <div class="flex items-center gap-1.5" title="Contacted">
                        <i class="fa-solid fa-paper-plane"></i>
                        <span class="font-semibold" id="stat-contacted">{{ stats.contacted }}</span>
                    </div>
                    <div class="flex items-center gap-1.5" title="Replied">
                        <i class="fa-solid fa-reply"></i>
                        <span class="font-semibold" id="stat-replied">{{ stats.replied }}</span>
                    </div>
                    <div class="flex items-center gap-1.5" title="Interested">
                        <i class="fa-regular fa-handshake"></i>
                        <span class="font-semibold" id="stat-interested">{{ stats.interested }}</span>
                    </div>
                    <div class="flex items-center gap-1.5" title="Converted">
                        <i class="fa-solid fa-circle-check"></i>
                        <span class="font-semibold" id="stat-converted">{{ stats.converted }}</span>
                    </div>
                </div>
            </div>
            <div class="flex items-center gap-3">
                <div class="dropdown dropdown-end">
                    <div tabindex="0" role="button" class="btn bg-white border border-base-300 h-10 min-h-10 rounded-lg px-4 text-sm">
                        <i class="fa-solid fa-filter mr-2 text-gray-500"></i>Filters
                    </div>
                    <div tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-white rounded-box w-52 border border-gray-200">
                        <div class="p-2">
                            <label class="label">
                                <span class="label-text font-medium">Status</span>
                            </label>
                            <select id="status-filter"
                                    class="select select-bordered select-sm w-full bg-white"
                                    hx-trigger="change"
                                    hx-post="#"
                                    hx-target="#leads-table-body"
                                    hx-include="#search-input">
                                <option value="">All Statuses</option>
                                <option value="Not contacted">Not contacted</option>
                                <option value="Contacted">Contacted</option>
                                <option value="Replied">Replied</option>
                                <option value="Converted">Converted</option>
                                <option value="Bounced">Bounced</option>
                            </select>
                        </div>
                    </div>
                </div>
                <button class="btn btn-primary btn-outline btn-square h-10 w-10 min-h-10"><i class="fa-solid fa-wand-magic-sparkles"></i></button>
                <button class="btn btn-primary h-10 min-h-10 rounded-lg px-4 text-sm">Add Leads</button>
            </div>
        </div>

        <!-- Table Container -->
        <div class="mt-4 flex-grow overflow-x-auto rounded-lg border border-gray-200 bg-white">
            <table class="table-auto w-full custom-table">
                <thead>
                    <tr>
                        <th class="w-12 text-center"><input type="checkbox" class="checkbox checkbox-sm rounded" /></th>
                        <th class="w-16"></th> <!-- Index number column -->
                        <th class="text-left">Lead</th>
                        <th class="text-left">Company</th>
                        <th class="text-left">Status</th>
                        <th class="text-left">Links</th>
                        <th class="text-left">Converted at</th>
                        <th class="text-left">Created at</th>
                    </tr>
                </thead>
                <tbody id="leads-table-body">
                    {% for lead in leads %}
                    <tr class="hover:bg-gray-50 lead-row"
                        data-search="{{ lead.first_name|lower }} {{ lead.last_name|lower }} {{ lead.email|lower }} {{ lead.company|lower }}"
                        data-status="{{ lead.status }}">
                        <td class="text-center">
                            <input type="checkbox" class="checkbox checkbox-sm rounded" />
                        </td>
                        <td class="text-gray-500 text-center">{{ lead.id }}</td>
                        <td class="font-medium text-gray-700">
                            <div class="flex flex-col">
                                <span>{{ lead.first_name }} {{ lead.last_name }}</span>
                                <span class="text-sm text-gray-500">{{ lead.email }}</span>
                            </div>
                        </td>
                        <td class="text-gray-600">{{ lead.company }}</td>
                        <td>
                            <div class="badge {{ lead.status_class }} gap-2 font-medium py-3">
                                <i class="{{ lead.status_icon }}"></i>
                                {{ lead.status }}
                            </div>
                        </td>
                        <td class="text-gray-600">
                            {% if lead.links_count > 0 %}
                                <span class="badge badge-outline">{{ lead.links_count }} links</span>
                            {% else %}
                                <span class="text-gray-400">No links</span>
                            {% endif %}
                        </td>
                        <td class="text-gray-600">
                            {% if lead.converted_at %}
                                {{ lead.converted_at }}
                            {% else %}
                                <span class="text-gray-400">-</span>
                            {% endif %}
                        </td>
                        <td class="text-gray-600">{{ lead.created_at }}</td>
                    </tr>
                    {% empty %}
                    <tr id="no-leads-row">
                        <td colspan="8" class="text-center py-8 text-gray-500">
                            <i class="fa-solid fa-users text-2xl mb-2"></i>
                            <p>No leads found for this campaign</p>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        <!-- Footer -->
        <div class="py-6 text-center">
            <hr class="mb-4">
            <div class="flex items-center justify-center gap-4">
                <span class="text-sm text-gray-400" id="search-status">Showing all leads</span>
                <button class="btn btn-sm bg-white border border-gray-300" disabled>No more to load</button>
            </div>
        </div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search-input');
    const statusFilter = document.getElementById('status-filter');
    const leadRows = document.querySelectorAll('.lead-row');
    const searchStatus = document.getElementById('search-status');

    // Stats elements
    const statTotal = document.getElementById('stat-total');
    const statViewed = document.getElementById('stat-viewed');
    const statContacted = document.getElementById('stat-contacted');
    const statReplied = document.getElementById('stat-replied');
    const statInterested = document.getElementById('stat-interested');
    const statConverted = document.getElementById('stat-converted');

    function filterLeads() {
        const searchTerm = searchInput.value.toLowerCase().trim();
        const selectedStatus = statusFilter.value;

        let visibleCount = 0;
        let stats = {
            total: 0,
            viewed: 0,
            contacted: 0,
            replied: 0,
            interested: 0,
            converted: 0
        };

        leadRows.forEach(row => {
            const searchData = row.getAttribute('data-search');
            const status = row.getAttribute('data-status');

            const matchesSearch = !searchTerm || searchData.includes(searchTerm);
            const matchesStatus = !selectedStatus || status === selectedStatus;

            if (matchesSearch && matchesStatus) {
                row.style.display = '';
                visibleCount++;

                // Update stats for visible leads
                stats.total++;
                if (['Contacted', 'Replied', 'Converted'].includes(status)) {
                    stats.viewed++;
                    stats.contacted++;
                }
                if (['Replied', 'Converted'].includes(status)) {
                    stats.replied++;
                }
                if (status === 'Converted') {
                    stats.converted++;
                }
            } else {
                row.style.display = 'none';
            }
        });

        // Update stats display
        statTotal.textContent = stats.total;
        statViewed.textContent = stats.viewed;
        statContacted.textContent = stats.contacted;
        statReplied.textContent = stats.replied;
        statInterested.textContent = stats.interested;
        statConverted.textContent = stats.converted;

        // Update search status
        if (searchTerm || selectedStatus) {
            searchStatus.textContent = `Showing ${visibleCount} of {{ stats.total_leads }} leads`;
        } else {
            searchStatus.textContent = 'Showing all leads';
        }

        // Show/hide no results message
        const noResultsRow = document.getElementById('no-results-row');
        if (noResultsRow) {
            noResultsRow.remove();
        }

        if (visibleCount === 0 && leadRows.length > 0) {
            const tbody = document.getElementById('leads-table-body');
            const noResultsHtml = `
                <tr id="no-results-row">
                    <td colspan="8" class="text-center py-8 text-gray-500">
                        <i class="fa-solid fa-search text-2xl mb-2"></i>
                        <p>No leads match your search criteria</p>
                        <button class="btn btn-sm btn-ghost mt-2" onclick="clearFilters()">Clear filters</button>
                    </td>
                </tr>
            `;
            tbody.insertAdjacentHTML('beforeend', noResultsHtml);
        }
    }

    // Clear filters function
    window.clearFilters = function() {
        searchInput.value = '';
        statusFilter.value = '';
        filterLeads();
    };

    // Event listeners
    searchInput.addEventListener('input', filterLeads);
    statusFilter.addEventListener('change', filterLeads);

    // Prevent HTMX from interfering with our frontend filtering
    searchInput.removeAttribute('hx-trigger');
    searchInput.removeAttribute('hx-post');
    searchInput.removeAttribute('hx-target');
    searchInput.removeAttribute('hx-include');

    statusFilter.removeAttribute('hx-trigger');
    statusFilter.removeAttribute('hx-post');
    statusFilter.removeAttribute('hx-target');
    statusFilter.removeAttribute('hx-include');
});
</script>

      {% endblock %}