 {% extends "app/campaign/campaign-base.html" %}
 
 {% block title %}Campaign Leads{% endblock %}
 
 
 
 {% block cmp-base-content %}     
     
     
     
     
       
  
  
  
  


        <!-- Toolbar -->
        <div class="flex items-center justify-between mt-8">
            <div class="flex items-center gap-4">
                <label class="input input-bordered flex items-center gap-2 h-10 w-64 bg-white">
                    <i class="fa-solid fa-magnifying-glass text-gray-400"></i>
                    <input type="text"
                           name="search"
                           id="search-input"
                           class="grow text-sm"
                           placeholder="Search leads..."
                           hx-get="{% url 'campaign_leads_filter' campaign_id %}"
                           hx-trigger="keyup changed delay:300ms"
                           hx-target="#leads-container"
                           hx-include="#status-filter" />
                </label>
                <div id="stats-container" class="flex items-center gap-4 text-gray-500 text-sm p-2 rounded-lg bg-gray-100">
                    <div class="flex items-center gap-1.5" title="Total Leads">
                        <i class="fa-solid fa-users"></i>
                        <span class="font-semibold">{{ stats.total_leads }}</span>
                    </div>
                    <div class="flex items-center gap-1.5" title="Viewed">
                        <i class="fa-solid fa-eye"></i>
                        <span class="font-semibold">{{ stats.viewed }}</span>
                    </div>
                    <div class="flex items-center gap-1.5" title="Contacted">
                        <i class="fa-solid fa-paper-plane"></i>
                        <span class="font-semibold">{{ stats.contacted }}</span>
                    </div>
                    <div class="flex items-center gap-1.5" title="Replied">
                        <i class="fa-solid fa-reply"></i>
                        <span class="font-semibold">{{ stats.replied }}</span>
                    </div>
                    <div class="flex items-center gap-1.5" title="Interested">
                        <i class="fa-regular fa-handshake"></i>
                        <span class="font-semibold">{{ stats.interested }}</span>
                    </div>
                    <div class="flex items-center gap-1.5" title="Converted">
                        <i class="fa-solid fa-circle-check"></i>
                        <span class="font-semibold">{{ stats.converted }}</span>
                    </div>
                </div>
            </div>
            <div class="flex items-center gap-3">
                <div class="dropdown dropdown-end">
                    <div tabindex="0" role="button" class="btn bg-white border border-base-300 h-10 min-h-10 rounded-lg px-4 text-sm">
                        <i class="fa-solid fa-filter mr-2 text-gray-500"></i>Filters
                    </div>
                    <div tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-white rounded-box w-52 border border-gray-200">
                        <div class="p-2">
                            <label class="label">
                                <span class="label-text font-medium">Status</span>
                            </label>
                            <select name="status"
                                    id="status-filter"
                                    class="select select-bordered select-sm w-full bg-white"
                                    hx-get="{% url 'campaign_leads_filter' campaign_id %}"
                                    hx-trigger="change"
                                    hx-target="#leads-container"
                                    hx-include="#search-input">
                                <option value="">All Statuses</option>
                                <option value="Not contacted">Not contacted</option>
                                <option value="Contacted">Contacted</option>
                                <option value="Replied">Replied</option>
                                <option value="Converted">Converted</option>
                                <option value="Bounced">Bounced</option>
                            </select>
                        </div>
                    </div>
                </div>
                <button class="btn btn-primary btn-outline btn-square h-10 w-10 min-h-10"><i class="fa-solid fa-wand-magic-sparkles"></i></button>
                <button class="btn btn-primary h-10 min-h-10 rounded-lg px-4 text-sm">Add Leads</button>
            </div>
        </div>

        <!-- Leads Container -->
        <div id="leads-container">
            <!-- Bulk Actions Bar -->
            <div id="bulk-actions" class="hidden bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4 mt-4">
            <div class="flex items-center justify-between">
                <span class="text-sm text-blue-700">
                    <span id="selected-count">0</span> leads selected
                </span>
                <div class="flex items-center gap-2">
                    <button class="btn btn-sm btn-error"
                            onclick="deleteSelected()"
                            id="delete-selected-btn">
                        <i class="fa-solid fa-trash mr-1"></i>
                        Delete Selected
                    </button>
                    <button class="btn btn-sm btn-ghost" onclick="clearSelection()">
                        Clear Selection
                    </button>
                </div>
            </div>
        </div>

        <!-- Table Container -->
        <div class="mt-4 flex-grow overflow-x-auto rounded-lg border border-gray-200 bg-white">
            <table class="table-auto w-full custom-table">
                <thead>
                    <tr>
                        <th class="w-12 text-center">
                            <input type="checkbox"
                                   class="checkbox checkbox-sm rounded"
                                   id="select-all-checkbox"
                                   onchange="toggleSelectAll()" />
                        </th>
                        <th class="w-16"></th> <!-- Index number column -->
                        <th class="text-left">Lead</th>
                        <th class="text-left">Company</th>
                        <th class="text-left">Status</th>
                        <th class="text-left">Links</th>
                        <th class="text-left">Converted at</th>
                        <th class="text-left">Created at</th>
                        <th class="w-12"></th> <!-- Actions column -->
                    </tr>
                </thead>
                <tbody id="leads-table-body">
                    {% for lead in leads %}
                    <tr class="hover:bg-gray-50 lead-row">
                        <td class="text-center">
                            <input type="checkbox"
                                   class="checkbox checkbox-sm rounded lead-checkbox"
                                   value="{{ lead.id }}"
                                   onchange="updateBulkActions()" />
                        </td>
                        <td class="text-gray-500 text-center">{{ lead.id }}</td>
                        <td class="font-medium text-gray-700">
                            <div class="flex flex-col">
                                <span>{{ lead.first_name }} {{ lead.last_name }}</span>
                                <span class="text-sm text-gray-500">{{ lead.email }}</span>
                            </div>
                        </td>
                        <td class="text-gray-600">{{ lead.company }}</td>
                        <td>
                            <div class="badge {{ lead.status_class }} gap-2 font-medium py-3">
                                <i class="{{ lead.status_icon }}"></i>
                                {{ lead.status }}
                            </div>
                        </td>
                        <td class="text-gray-600">
                            {% if lead.links_count > 0 %}
                                <span class="badge badge-outline">{{ lead.links_count }} links</span>
                            {% else %}
                                <span class="text-gray-400">No links</span>
                            {% endif %}
                        </td>
                        <td class="text-gray-600">
                            {% if lead.converted_at %}
                                {{ lead.converted_at }}
                            {% else %}
                                <span class="text-gray-400">-</span>
                            {% endif %}
                        </td>
                        <td class="text-gray-600">{{ lead.created_at }}</td>
                        <td class="text-center">
                            <div class="dropdown dropdown-end">
                                <div tabindex="0" role="button" class="btn btn-ghost btn-sm">
                                    <i class="fa-solid fa-ellipsis-vertical"></i>
                                </div>
                                <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-white rounded-box w-32 border border-gray-200">
                                    <li><a onclick="deleteLead({{ lead.id }})" class="text-red-600"><i class="fa-solid fa-trash mr-2"></i>Delete</a></li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr id="no-leads-row">
                        <td colspan="9" class="text-center py-16">
                            <div class="flex flex-col items-center justify-center">
                                <!-- SVG Illustration -->
                                <div class="mb-6 opacity-90">
                                    <img src="https://raw.githubusercontent.com/Othunderlight/static/refs/heads/main/no-campaign-lead.svg"
                                         alt="No leads illustration"
                                         class="w-64 h-auto mx-auto">
                                </div>

                                <!-- Message -->
                                <div class="text-center">
                                    <h3 class="text-lg font-semibold text-gray-800 mb-2">
                                        👋 Add some leads to get started
                                    </h3>
                                    <p class="text-gray-500 mb-6">
                                        Upload your leads or add them manually to start your campaign
                                    </p>

                                    <!-- Add Leads Button -->
                                    <button class="btn btn-primary h-10 min-h-10 rounded-lg px-4 text-sm">
                                        Add Leads
                                    </button>
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        </div> <!-- End leads-container -->

        <!-- Footer -->
        <div class="py-6 text-center">
            <hr class="mb-4">
            <div class="flex items-center justify-center gap-4">
                <span class="text-sm text-gray-400" id="search-status">Showing all leads</span>
                <button class="btn btn-sm bg-white border border-gray-300" disabled>No more to load</button>
            </div>
        </div>

<script>
// Bulk selection functionality
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    const leadCheckboxes = document.querySelectorAll('.lead-checkbox');

    leadCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateBulkActions();
}

function updateBulkActions() {
    const leadCheckboxes = document.querySelectorAll('.lead-checkbox');
    const checkedBoxes = document.querySelectorAll('.lead-checkbox:checked');
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    const bulkActions = document.getElementById('bulk-actions');
    const selectedCount = document.getElementById('selected-count');

    // Update select all checkbox state
    if (checkedBoxes.length === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    } else if (checkedBoxes.length === leadCheckboxes.length) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
    } else {
        selectAllCheckbox.indeterminate = true;
        selectAllCheckbox.checked = false;
    }

    // Show/hide bulk actions
    if (checkedBoxes.length > 0) {
        bulkActions.classList.remove('hidden');
        selectedCount.textContent = checkedBoxes.length;
    } else {
        bulkActions.classList.add('hidden');
    }
}

function clearSelection() {
    const leadCheckboxes = document.querySelectorAll('.lead-checkbox');
    const selectAllCheckbox = document.getElementById('select-all-checkbox');

    leadCheckboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    selectAllCheckbox.checked = false;
    selectAllCheckbox.indeterminate = false;

    updateBulkActions();
}

function deleteSelected() {
    const checkedBoxes = document.querySelectorAll('.lead-checkbox:checked');
    const leadIds = Array.from(checkedBoxes).map(cb => cb.value);

    if (leadIds.length === 0) return;

    // Use the new modal system
    ModalSystem.confirm({
        title: 'Delete Selected Leads',
        message: `Are you sure you want to delete ${leadIds.length} selected lead(s)? This action cannot be undone.`,
        confirmText: 'Delete',
        confirmClass: 'btn-error',
        action: function() {
            // Simulate deletion by removing rows
            checkedBoxes.forEach(checkbox => {
                const row = checkbox.closest('tr');
                row.remove();
            });

            // Clear selection
            clearSelection();

            // Show success toast
            ModalSystem.toast(`${leadIds.length} lead(s) deleted successfully!`);

            // Trigger HTMX refresh to update stats
            htmx.trigger('#search-input', 'keyup');
        }
    });
}

function deleteLead(leadId) {
    // Use the new modal system
    ModalSystem.confirm({
        title: 'Delete Lead',
        message: 'Are you sure you want to delete this lead? This action cannot be undone.',
        confirmText: 'Delete',
        confirmClass: 'btn-error',
        action: function() {
            // Find and remove the row
            const checkbox = document.querySelector(`input[value="${leadId}"]`);
            if (checkbox) {
                const row = checkbox.closest('tr');
                row.remove();
            }

            // Update bulk actions
            updateBulkActions();

            // Show success toast
            ModalSystem.toast('Lead deleted successfully!');

            // Trigger HTMX refresh to update stats
            htmx.trigger('#search-input', 'keyup');
        }
    });
}

function clearFilters() {
    document.getElementById('search-input').value = '';
    document.getElementById('status-filter').value = '';
    htmx.trigger('#search-input', 'keyup');
}



// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    updateBulkActions();
});

// Re-initialize after HTMX updates
document.addEventListener('htmx:afterSwap', function() {
    updateBulkActions();
});
</script>


{% endblock %}