 {% extends "app/campaign/campaign-base.html" %}

{% block title %}Campaign sequence{% endblock %}


  {% block cmp-base-content %}     

        <!-- div two-column layout -->
        <div class="flex-1 flex gap-6 mt-6 overflow-hidden">
          <!-- Left Column: Steps -->
          <div class="w-full max-w-sm flex-shrink-0 flex flex-col gap-4 overflow-y-auto pr-4 steps-list">
            <!-- Step 1 Card -->
            <div class="card bg-white border border-base-200 rounded-lg p-4">
              <div class="flex justify-between items-center">
                <h3 class="font-bold text-gray-800">Step 1</h3>
                <button class="btn btn-ghost btn-sm btn-square text-gray-400"><i class="fa-regular fa-copy"></i></button>
              </div>
              <div class="mt-4 space-y-4">
                <div class="w-full text-left p-3 border border-base-300 rounded-lg text-gray-400 text-sm"><Empty subject></div>
                <button class="btn btn-sm btn-ghost text-primary w-full"><i class="fa-solid fa-plus"></i> Add variant</button>
              </div>
              <div class="flex items-center justify-between mt-4 text-sm text-gray-500">
                <span>Send next message in</span>
                <div class="flex items-center gap-2">
                  <input type="number" value="1" class="input input-bordered input-sm w-16 text-center bg-white" />
                  <span>Days</span>
                </div>
              </div>
            </div>

            <!-- Step 2 Card (Active) -->
            <div class="card bg-white border-2 border-primary rounded-lg p-4">
              <div class="flex justify-between items-center">
                <h3 class="font-bold text-gray-800">Step 2</h3>
                <button class="btn btn-ghost btn-sm btn-square text-gray-400"><i class="fa-regular fa-copy"></i></button>
              </div>
              <div class="mt-4 space-y-4">
                <div class="w-full text-left p-3 border border-base-300 rounded-lg text-gray-400 text-sm"><Previous email's subject></div>
                <button class="btn btn-sm btn-ghost text-primary w-full"><i class="fa-solid fa-plus"></i> Add variant</button>
              </div>
              <div class="flex items-center justify-between mt-4 text-sm text-gray-500">
                <span>Send next message in</span>
                <div class="flex items-center gap-2">
                  <input type="number" value="1" class="input input-bordered input-sm w-16 text-center bg-white" />
                  <span>Days</span>
                </div>
              </div>
            </div>

            <!-- Step 3 Card -->
            <div class="card bg-white border border-base-200 rounded-lg p-4">
              <div class="flex justify-between items-center">
                <h3 class="font-bold text-gray-800">Step 3</h3>
                <button class="btn btn-ghost btn-sm btn-square text-gray-400"><i class="fa-regular fa-copy"></i></button>
              </div>
              <!-- Content can be added here if needed -->
            </div>
          </div>

          <!-- Right Column: Editor -->
          <div class="flex-1 flex flex-col bg-white border border-base-200 rounded-lg">
            <div class="flex justify-between items-center p-4 border-b border-base-200">
              <div class="text-sm">
                <span class="font-bold text-gray-800">Subject</span>
                <span class="text-gray-500 ml-2">Leave empty to use previous step's subject</span>
              </div>
              <div class="flex items-center gap-2">
                <button class="btn btn-sm bg-white border border-base-300"><i class="fa-regular fa-eye mr-2"></i>Preview</button>
                <button class="btn btn-sm btn-square bg-white border border-base-300"><i class="fa-solid fa-bolt text-blue-500"></i></button>
              </div>
            </div>
            <div class="flex-grow p-4">
              <!-- This is the empty editor area -->
            </div>
          </div>
        </div>




{% endblock %}