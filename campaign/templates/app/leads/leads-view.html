{% extends "base-app.html" %}

{% block title %}Leads{% endblock %}

{% block content %}
<div class="flex-1 flex flex-col overflow-y-auto bg-gray-50 p-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Leads</h1>
        <p class="text-gray-600">Manage your leads and contacts for campaigns</p>
    </div>

    <!-- Main Content -->
    <div class="flex-1">
        <!-- Toolbar -->
        <div class="flex items-center justify-between mb-6">
            <div class="flex items-center gap-4">
                <label class="input input-bordered flex items-center gap-2 h-10 w-72 bg-white">
                    <i class="fa-solid fa-magnifying-glass text-gray-400"></i>
                    <input type="text" class="grow text-sm" placeholder="Search leads..." />
                </label>
            </div>
            <div class="flex items-center gap-3">
                <div class="dropdown dropdown-end">
                    <button tabindex="0" role="button" class="btn bg-white hover:bg-base-200 border border-base-300 h-10 min-h-10 px-3 flex items-center gap-2 rounded-lg">
                        <span class="text-sm font-medium text-gray-700">All leads</span>
                        <i class="fa-solid fa-chevron-down text-xs text-gray-500"></i>
                    </button>
                </div>
                <button class="btn btn-primary h-10 min-h-10 rounded-lg px-4 text-sm">
                    <i class="fa-solid fa-plus mr-1"></i>
                    Add Lead
                </button>
            </div>
        </div>

        <!-- Table / List Area -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <!-- Table Header -->
            <div class="grid grid-cols-[48px_2fr_1fr_1fr_1fr_1fr_48px] gap-4 items-center px-4 py-3 border-b border-gray-200">
                <label>
                    <input type="checkbox" class="checkbox checkbox-sm rounded" />
                </label>
                <span class="text-sm font-semibold text-gray-700">Name</span>
                <span class="text-sm font-semibold text-gray-700">Email</span>
                <span class="text-sm font-semibold text-gray-700">Company</span>
                <span class="text-sm font-semibold text-gray-700">Status</span>
                <span class="text-sm font-semibold text-gray-700">Added</span>
                <span></span> <!-- For actions column -->
            </div>

            <!-- Table Rows -->
            <div class="divide-y divide-gray-100">
                <!-- Sample Lead Row -->
                <div class="grid grid-cols-[48px_2fr_1fr_1fr_1fr_1fr_48px] gap-4 items-center px-4 py-3 hover:bg-gray-50">
                    <label>
                        <input type="checkbox" class="checkbox checkbox-sm rounded" />
                    </label>
                    <div class="font-medium text-gray-900">John Smith</div>
                    <div class="text-gray-600 text-sm"><EMAIL></div>
                    <div class="text-gray-600 text-sm">Acme Inc</div>
                    <div>
                        <span class="badge badge-sm bg-green-100 text-green-800 font-medium">Active</span>
                    </div>
                    <div class="text-gray-600 text-sm">Oct 15, 2023</div>
                    <div class="flex justify-end">
                        <button class="btn btn-ghost btn-sm btn-circle">
                            <i class="fa-solid fa-ellipsis-vertical text-gray-500"></i>
                        </button>
                    </div>
                </div>

                <!-- Empty State (shown when no leads) -->
                <div class="hidden py-12 text-center">
                    <div class="inline-flex h-20 w-20 items-center justify-center rounded-full bg-gray-100 text-gray-400 mb-4">
                        <i class="fa-solid fa-user-plus text-3xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No leads yet</h3>
                    <p class="text-gray-500 max-w-md mx-auto mb-6">
                        Start by adding your first lead to begin creating targeted campaigns
                    </p>
                    <button class="btn btn-primary">
                        <i class="fa-solid fa-plus mr-2"></i>
                        Add Your First Lead
                    </button>
                </div>
            </div>

            <!-- Pagination -->
            <div class="flex items-center justify-between border-t border-gray-200 px-4 py-3">
                <div class="text-sm text-gray-500">
                    Showing <span class="font-medium">1</span> to <span class="font-medium">10</span> of <span class="font-medium">20</span> leads
                </div>
                <div class="flex items-center gap-2">
                    <button class="btn btn-sm btn-ghost">
                        <i class="fa-solid fa-chevron-left text-gray-500"></i>
                    </button>
                    <button class="btn btn-sm btn-ghost bg-primary/10 text-primary">1</button>
                    <button class="btn btn-sm btn-ghost">2</button>
                    <button class="btn btn-sm btn-ghost">
                        <i class="fa-solid fa-chevron-right text-gray-500"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}