{% extends "base-app.html" %}

{% block title %}Overall Dashboard{% endblock %}

{% block content %}
<div class="flex-1 flex flex-col overflow-y-auto bg-gray-50 p-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Overall Dashboard</h1>
        <p class="text-gray-600">Get a comprehensive view of all your campaigns and performance metrics</p>
    </div>

    <!-- Main Content -->
    <div class="flex-1 flex items-center justify-center">
        <div class="text-center max-w-md">
            <!-- Icon -->
            <div class="mb-6">
                <div class="inline-flex h-20 w-20 items-center justify-center rounded-full bg-indigo-100 text-indigo-600">
                    <i class="fa-solid fa-chart-line text-3xl"></i>
                </div>
            </div>
            
            <!-- Title -->
            <h2 class="text-2xl font-semibold text-gray-900 mb-4">Overall Dashboard</h2>
            
            <!-- Description -->
            <p class="text-gray-600 mb-8 leading-relaxed">
                Your central hub for monitoring all campaign performance, revenue tracking, and business insights. 
                Get a bird's-eye view of your entire email marketing operation.
            </p>
            
            <!-- Features List -->
            <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200 mb-8">
                <h3 class="font-semibold text-gray-900 mb-4">Coming Features:</h3>
                <ul class="text-left space-y-2 text-gray-600">
                    <li class="flex items-center gap-2">
                        <i class="fa-solid fa-check text-green-500 text-sm"></i>
                        Cross-campaign analytics
                    </li>
                    <li class="flex items-center gap-2">
                        <i class="fa-solid fa-check text-green-500 text-sm"></i>
                        Revenue & ROI tracking
                    </li>
                    <li class="flex items-center gap-2">
                        <i class="fa-solid fa-check text-green-500 text-sm"></i>
                        Performance comparisons
                    </li>
                    <li class="flex items-center gap-2">
                        <i class="fa-solid fa-check text-green-500 text-sm"></i>
                        Trend analysis & forecasting
                    </li>
                    <li class="flex items-center gap-2">
                        <i class="fa-solid fa-check text-green-500 text-sm"></i>
                        Custom reporting & exports
                    </li>
                </ul>
            </div>
            
            <!-- Action Button -->
            <button class="btn btn-primary" onclick="showComingSoon()">
                <i class="fa-solid fa-chart-bar mr-2"></i>
                View Analytics
            </button>
        </div>
    </div>
</div>

<script>
function showComingSoon() {
    if (window.ModalSystem) {
        window.ModalSystem.toast('Overall dashboard coming soon!', 'info');
    } else {
        alert('Overall dashboard coming soon!');
    }
}
</script>
{% endblock %}
