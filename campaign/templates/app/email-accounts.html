{% extends "base-app.html" %}

{% block title %}Email Accounts{% endblock %}

{% block content %}
<div class="flex-1 flex flex-col overflow-y-auto bg-gray-50 p-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Email Accounts</h1>
        <p class="text-gray-600">Connect and manage your email accounts for campaigns</p>
    </div>

    <!-- Main Content -->
    <div class="flex-1 flex items-center justify-center">
        <div class="text-center max-w-md">
            <!-- Icon -->
            <div class="mb-6">
                <div class="inline-flex h-20 w-20 items-center justify-center rounded-full bg-green-100 text-green-600">
                    <i class="fa-solid fa-at text-3xl"></i>
                </div>
            </div>
            
            <!-- Title -->
            <h2 class="text-2xl font-semibold text-gray-900 mb-4">Email Accounts Management</h2>
            
            <!-- Description -->
            <p class="text-gray-600 mb-8 leading-relaxed">
                Connect your email accounts to send campaigns. Manage SMTP settings, authentication, 
                and monitor sending reputation across all your connected email providers.
            </p>
            
            <!-- Features List -->
            <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200 mb-8">
                <h3 class="font-semibold text-gray-900 mb-4">Coming Features:</h3>
                <ul class="text-left space-y-2 text-gray-600">
                    <li class="flex items-center gap-2">
                        <i class="fa-solid fa-check text-green-500 text-sm"></i>
                        Gmail & Outlook integration
                    </li>
                    <li class="flex items-center gap-2">
                        <i class="fa-solid fa-check text-green-500 text-sm"></i>
                        Custom SMTP configuration
                    </li>
                    <li class="flex items-center gap-2">
                        <i class="fa-solid fa-check text-green-500 text-sm"></i>
                        Email reputation monitoring
                    </li>
                    <li class="flex items-center gap-2">
                        <i class="fa-solid fa-check text-green-500 text-sm"></i>
                        Sending limits management
                    </li>
                    <li class="flex items-center gap-2">
                        <i class="fa-solid fa-check text-green-500 text-sm"></i>
                        Authentication & security
                    </li>
                </ul>
            </div>
            
            <!-- Action Button -->
            <button class="btn btn-primary" onclick="showComingSoon()">
                <i class="fa-solid fa-plus mr-2"></i>
                Connect Email Account
            </button>
        </div>
    </div>
</div>

<script>
function showComingSoon() {
    if (window.ModalSystem) {
        window.ModalSystem.toast('Email accounts management coming soon!', 'info');
    } else {
        alert('Email accounts management coming soon!');
    }
}
</script>
{% endblock %}
