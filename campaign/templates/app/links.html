{% extends "base-app.html" %}

{% block title %}Links{% endblock %}

{% block content %}
<div class="flex-1 flex flex-col overflow-y-auto bg-gray-50 p-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Links</h1>
        <p class="text-gray-600">Track and manage all your campaign links and redirects</p>
    </div>

    <!-- Main Content -->
    <div class="flex-1 flex items-center justify-center">
        <div class="text-center max-w-md">
            <!-- Icon -->
            <div class="mb-6">
                <div class="inline-flex h-20 w-20 items-center justify-center rounded-full bg-orange-100 text-orange-600">
                    <i class="fa-solid fa-link text-3xl"></i>
                </div>
            </div>
            
            <!-- Title -->
            <h2 class="text-2xl font-semibold text-gray-900 mb-4">Links Management</h2>
            
            <!-- Description -->
            <p class="text-gray-600 mb-8 leading-relaxed">
                Monitor and analyze all your campaign links. Track click-through rates, manage redirects, 
                and get detailed analytics on link performance across all campaigns.
            </p>
            
            <!-- Features List -->
            <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200 mb-8">
                <h3 class="font-semibold text-gray-900 mb-4">Coming Features:</h3>
                <ul class="text-left space-y-2 text-gray-600">
                    <li class="flex items-center gap-2">
                        <i class="fa-solid fa-check text-green-500 text-sm"></i>
                        Link tracking & analytics
                    </li>
                    <li class="flex items-center gap-2">
                        <i class="fa-solid fa-check text-green-500 text-sm"></i>
                        Custom short URLs
                    </li>
                    <li class="flex items-center gap-2">
                        <i class="fa-solid fa-check text-green-500 text-sm"></i>
                        UTM parameter management
                    </li>
                    <li class="flex items-center gap-2">
                        <i class="fa-solid fa-check text-green-500 text-sm"></i>
                        Click heatmaps & timing
                    </li>
                    <li class="flex items-center gap-2">
                        <i class="fa-solid fa-check text-green-500 text-sm"></i>
                        Conversion tracking
                    </li>
                </ul>
            </div>
            
            <!-- Action Button -->
            <button class="btn btn-primary" onclick="showComingSoon()">
                <i class="fa-solid fa-plus mr-2"></i>
                Create Tracked Link
            </button>
        </div>
    </div>
</div>

<script>
function showComingSoon() {
    if (window.ModalSystem) {
        window.ModalSystem.toast('Links management coming soon!', 'info');
    } else {
        alert('Links management coming soon!');
    }
}
</script>
{% endblock %}
