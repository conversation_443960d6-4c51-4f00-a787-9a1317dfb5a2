<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Create New Campaign - Clone</title>
  
  <!-- <PERSON><PERSON> and Tailwind CSS -->
  <link href="https://cdn.jsdelivr.net/npm/daisyui@4.12.2/dist/full.css" rel="stylesheet" type="text/css" />
  <script src="https://cdn.tailwindcss.com"></script>
  
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A==" crossorigin="anonymous" referrerpolicy="no-referrer" />

  <style>
    /* Custom styles to better match the screenshot */
    body {
      /* This page has a pure white background */
      background-color: #ffffff; 
      font-family: sans-serif;
    }
    .btn {
        text-transform: none; /* Prevent DaisyUI from uppercasing button text */
        font-weight: 600; /* Buttons seem to have bold text */
    }
  </style>
</head>
<body class="bg-white">

  <!-- Main container for the page -->
  <div class="relative min-h-screen flex flex-col font-sans">
    
    <!-- Back Button -->
    <header class="absolute top-0 left-0 p-4 sm:p-6 z-10">
      <a href="#" class="inline-flex items-center gap-2.5 text-gray-800 font-semibold text-base hover:text-black">
        <i class="fa-solid fa-chevron-left text-sm"></i>
        <span>Back</span>
      </a>
    </header>

    <!-- Centered Content -->
    <main class="flex-grow flex items-center justify-center">
      <div class="w-full max-w-lg text-center px-4 sm:px-6">
        
        <h1 class="text-3xl sm:text-4xl font-bold text-gray-800">
          Let's create a new campaign
        </h1>
        <p class="mt-3 text-base text-gray-600">
          What would you like to name it?
        </p>
        
        <div class="mt-10 text-left">
          <label for="campaignName" class="text-sm font-medium text-gray-700">
            Campaign Name
          </label>
          <input 
            type="text" 
            id="campaignName" 
            value="My Campaign" 
            class="input input-bordered w-full mt-2 text-2xl h-16"
          />
        </div>

        <div class="mt-8 flex justify-center items-center gap-6">
          <a href="#" class="font-semibold text-blue-600 hover:text-blue-700">Cancel</a>
          <button class="btn btn-primary rounded-lg px-6 py-3 h-auto text-base">
            Continue >
          </button>
        </div>

      </div>
    </main>

  </div>

  <!-- Floating Action Button -->
  <div class="fixed bottom-5 right-5 z-20">
    <button class="btn btn-primary btn-circle w-14 h-14 shadow-lg">
        <!-- The icon in the screenshot is custom. Using a standard Font Awesome icon as a close representation. -->
        <i class="fa-regular fa-face-smile text-2xl"></i>
    </button>
  </div>
  
  <script>
    // No custom JavaScript is needed for this layout.
  </script>

</body>
</html>