<!DOCTYPE html>
<html lang="en" data-theme="custom">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Custom Theme Example</title>



  <script>
    tailwind.config = {
      theme: {
        extend: {
          fontFamily: {
            sans: [
              'Product Sans', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI',
              'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif'
            ],
            'product-sans': ['Product Sans', 'sans-serif'],
          },
          colors: {
            slate: { dark: '#141413', medium: '#3d3d3a', light: '#5e5d59' },
            cloud: { dark: '#87867f', medium: '#b0aea5', light: '#d1cfc5' },
            ivory: { dark: '#e8e6dc', medium: '#f0eee6', light: '#faf9f5' },
            book: { cloth: '#cc785c', clothLight: '#d2886f' },
            kraft: { DEFAULT: '#d4a27f', light: '#daaf91' },
            clay: '#d97757',
            lilac: '#8989de',
            lilypad: '#7ebf8e',
            manilla: { DEFAULT: '#ebdbbc', light: '#f1e6d0' },
            eggshell: '#f6f1eb',
            focus: '#61aaf2',
            error: '#bf4d43',
            dark: '#0f0f0e',
            light: '#fff'
          },
        }
      }
    }
  </script>

  <!-- DaisyUI + custom theme -->
  <script>
    window.daisyui = {
      themes: [
        {
          custom: {
            "primary": "#d97757",
            "primary-focus": "#cc785c",
            "primary-content": "#faf9f5",
            "secondary": "#7ebf8e",
            "secondary-focus": "#5e5d59",
            "secondary-content": "#faf9f5",
            "accent": "#8989de",
            "accent-focus": "#5e5d59",
            "accent-content": "#faf9f5",
            "neutral": "#3d3d3a",
            "neutral-focus": "#141413",
            "neutral-content": "#f0eee6",
            "base-100": "#faf9f5",
            "base-200": "#f0eee6",
            "base-300": "#e8e6dc",
            "base-content": "#141413",
            "info": "#61aaf2",
            "success": "#7ebf8e",
            "warning": "#d4a27f",
            "error": "#bf4d43",
            "book-cloth": "#cc785c",
            "kraft": "#d4a27f",
            "manilla": "#ebdbbc",
            "--rounded-box": "0.5rem",
            "--rounded-btn": "0.25rem",
            "--rounded-badge": "1rem"
          }
        }
      ]
    }
  </script>

    <!-- Product Sans font -->
  <link href="https://fonts.googleapis.com/css2?family=Product+Sans&display=swap" rel="stylesheet">

  <!-- DaisyUI and Tailwind CSS -->
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.12.2/dist/full.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>

    <script src="https://cdn.jsdelivr.net/npm/htmx.org@2.0.5/dist/htmx.min.js" integrity="sha384-t4DxZSyQK+0Uv4jzy5B0QyHyWQD2GFURUmxKMBVww9+e2EJ0ei/vCvv7+79z0fkr" crossorigin="anonymous"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="stylesheet" href="{% static 'style.css' %}">
</head>
<body class="font-sans">

  <!-- Hero Section -->
  <section class="min-h-screen bg-base-100 flex flex-col items-center justify-center text-center px-4">
    <h1 class="text-4xl font-bold text-base-content mb-4">Welcome to GataraAI</h1>
    <p class="text-lg text-base-content mb-8 max-w-xl">
      We help B2B SaaS companies automate marketing & sales with custom AI agents.
    </p>
    
    <div class="flex space-x-4">
      <button class="btn btn-outline btn-primary">
        <i class="fas fa-save mr-2"></i> Save
      </button>
      <button class="btn btn-primary">
        <i class="fas fa-rocket mr-2"></i> Launch
      </button>
    </div>
  </section>

</body>
</html>
