
{% extends 'base.html' %}
{% block content %}


    <div class="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden" style='font-family: Inter, "Noto Sans", sans-serif;'>
      <div class="layout-container flex h-full grow flex-col">
        <header class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f0f2f5] px-10 py-3">
          <div class="flex items-center gap-8">
            <div class="flex items-center gap-4 text-[#111518]">
              <div class="size-4">
                <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M24 45.8096C19.6865 45.8096 15.4698 44.5305 11.8832 42.134C8.29667 39.7376 5.50128 36.3314 3.85056 32.3462C2.19985 28.361 1.76794 23.9758 2.60947 19.7452C3.451 15.5145 5.52816 11.6284 8.57829 8.5783C11.6284 5.52817 15.5145 3.45101 19.7452 2.60948C23.9758 1.76795 28.361 2.19986 32.3462 3.85057C36.3314 5.50129 39.7376 8.29668 42.134 11.8833C44.5305 15.4698 45.8096 19.6865 45.8096 24L24 24L24 45.8096Z"
                    fill="currentColor"
                  ></path>
                </svg>
              </div>
              <h2 class="text-[#111518] text-lg font-bold leading-tight tracking-[-0.015em]">ConnectFlow</h2>
            </div>
            <div class="flex items-center gap-9">
              <a class="text-[#111518] text-sm font-medium leading-normal" href="#">Dashboard</a>
              <a class="text-[#111518] text-sm font-medium leading-normal" href="#">Campaigns</a>
              <a class="text-[#111518] text-sm font-medium leading-normal" href="#">Leads</a>
              <a class="text-[#111518] text-sm font-medium leading-normal" href="#">Products</a>
              <a class="text-[#111518] text-sm font-medium leading-normal" href="#">Reports</a>
            </div>
          </div>
          <div class="flex flex-1 justify-end gap-8">
            <label class="flex flex-col min-w-40 !h-10 max-w-64">
              <div class="flex w-full flex-1 items-stretch rounded-xl h-full">
                <div
                  class="text-[#60768a] flex border-none bg-[#f0f2f5] items-center justify-center pl-4 rounded-l-xl border-r-0"
                  data-icon="MagnifyingGlass"
                  data-size="24px"
                  data-weight="regular"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                    <path
                      d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"
                    ></path>
                  </svg>
                </div>
                <input
                  placeholder="Search"
                  class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#111518] focus:outline-0 focus:ring-0 border-none bg-[#f0f2f5] focus:border-none h-full placeholder:text-[#60768a] px-4 rounded-l-none border-l-0 pl-2 text-base font-normal leading-normal"
                  value=""
                />
              </div>
            </label>
            <button
              class="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 bg-[#f0f2f5] text-[#111518] gap-2 text-sm font-bold leading-normal tracking-[0.015em] min-w-0 px-2.5"
            >
              <div class="text-[#111518]" data-icon="Bell" data-size="20px" data-weight="regular">
                <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
                  <path
                    d="M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-43.92,16-80a64,64,0,1,1,128,0c0,36.05,8.28,66.73,16,80Z"
                  ></path>
                </svg>
              </div>
            </button>
            <div
              class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
              style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuCkBUBklJeQbnqQnFkO8N9U2qaTKvRnokOkCcvsc-qtw_mjaaZ8_JVsqAJ4k00V_Xy8eDMberOkAX_qoztbHifNQzRyPfbfxcgh7fY2Q2U_SdacDxB0SMhD0TsFrqhj3kU-FQO3quHsML3Hpf5kc_v2gN7KbZteZNI59dfd0aVPe5VtcI8z9EhehdZINy_JwfgkDVpsYQiuOcSOaJKsuvpN34VjoS0PpDuOIyDoCLk0NdeuIrVTfem6P_VC1jVasNV8jSJbtPxXuTI");'
            ></div>
          </div>
        </header>
        <div class="gap-1 px-6 flex flex-1 justify-center py-5">
          <div class="layout-content-container flex flex-col max-w-[920px] flex-1">
            <div class="flex flex-wrap justify-between gap-3 p-4"><p class="text-[#111518] tracking-light text-[32px] font-bold leading-tight min-w-72">Dashboard</p></div>
            <h3 class="text-[#111518] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Overall Performance Summary</h3>
            <div class="flex gap-3 p-3 flex-wrap pr-4">
              <button class="flex h-8 shrink-0 items-center justify-center gap-x-2 rounded-full bg-[#f0f2f5] pl-4 pr-2">
                <p class="text-[#111518] text-sm font-medium leading-normal">Last 30 Days</p>
                <div class="text-[#111518]" data-icon="CaretDown" data-size="20px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M213.66,101.66l-80,80a8,8,0,0,1-11.32,0l-80-80A8,8,0,0,1,53.66,90.34L128,164.69l74.34-74.35a8,8,0,0,1,11.32,11.32Z"></path>
                  </svg>
                </div>
              </button>
            </div>
            <div class="flex flex-wrap gap-4 p-4">
              <div class="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 border border-[#dbe1e6]">
                <p class="text-[#111518] text-base font-medium leading-normal">Total Active Campaigns</p>
                <p class="text-[#111518] tracking-light text-2xl font-bold leading-tight">12</p>
                <p class="text-[#078838] text-base font-medium leading-normal">+2 vs. Previous Period</p>
              </div>
              <div class="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 border border-[#dbe1e6]">
                <p class="text-[#111518] text-base font-medium leading-normal">Total Leads</p>
                <p class="text-[#111518] tracking-light text-2xl font-bold leading-tight">5,432</p>
                <p class="text-[#078838] text-base font-medium leading-normal">+500 vs. Previous Period</p>
              </div>
              <div class="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 border border-[#dbe1e6]">
                <p class="text-[#111518] text-base font-medium leading-normal">Messages Sent</p>
                <p class="text-[#111518] tracking-light text-2xl font-bold leading-tight">12,876</p>
                <p class="text-[#078838] text-base font-medium leading-normal">+1,000 vs. Previous Period</p>
              </div>
              <div class="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 border border-[#dbe1e6]">
                <p class="text-[#111518] text-base font-medium leading-normal">Conversions</p>
                <p class="text-[#111518] tracking-light text-2xl font-bold leading-tight">854</p>
                <p class="text-[#078838] text-base font-medium leading-normal">+50 vs. Previous Period</p>
              </div>
              <div class="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 border border-[#dbe1e6]">
                <p class="text-[#111518] text-base font-medium leading-normal">Overall Conversion Rate</p>
                <p class="text-[#111518] tracking-light text-2xl font-bold leading-tight">6.6%</p>
                <p class="text-[#078838] text-base font-medium leading-normal">+0.5% vs. Previous Period</p>
              </div>
            </div>
            <h3 class="text-[#111518] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Campaign Performance</h3>
            <div class="flex flex-wrap gap-4 px-4 py-6">
              <div class="flex min-w-72 flex-1 flex-col gap-2 rounded-xl border border-[#dbe1e6] p-6">
                <p class="text-[#111518] text-base font-medium leading-normal">Conversion Trend Over Time (vs. Previous Period)</p>
                <p class="text-[#111518] tracking-light text-[32px] font-bold leading-tight truncate">+15%</p>
                <div class="flex gap-1">
                  <p class="text-[#60768a] text-base font-normal leading-normal">Last 30 Days</p>
                  <p class="text-[#078838] text-base font-medium leading-normal">+15%</p>
                </div>
                <div class="flex min-h-[180px] flex-1 flex-col gap-8 py-4">
                  <svg width="100%" height="148" viewBox="-3 0 478 150" fill="none" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none">
                    <path
                      d="M0 109C18.1538 109 18.1538 21 36.3077 21C54.4615 21 54.4615 41 72.6154 41C90.7692 41 90.7692 93 108.923 93C127.077 93 127.077 33 145.231 33C163.385 33 163.385 101 181.538 101C199.692 101 199.692 61 217.846 61C236 61 236 45 254.154 45C272.308 45 272.308 121 290.462 121C308.615 121 308.615 149 326.769 149C344.923 149 344.923 1 363.077 1C381.231 1 381.231 81 399.385 81C417.538 81 417.538 129 435.692 129C453.846 129 453.846 25 472 25V149H326.769H0V109Z"
                      fill="url(#paint0_linear_1131_5935)"
                    ></path>
                    <path
                      d="M0 109C18.1538 109 18.1538 21 36.3077 21C54.4615 21 54.4615 41 72.6154 41C90.7692 41 90.7692 93 108.923 93C127.077 93 127.077 33 145.231 33C163.385 33 163.385 101 181.538 101C199.692 101 199.692 61 217.846 61C236 61 236 45 254.154 45C272.308 45 272.308 121 290.462 121C308.615 121 308.615 149 326.769 149C344.923 149 344.923 1 363.077 1C381.231 1 381.231 81 399.385 81C417.538 81 417.538 129 435.692 129C453.846 129 453.846 25 472 25"
                      stroke="#60768a"
                      stroke-width="3"
                      stroke-linecap="round"
                    ></path>
                    <defs>
                      <linearGradient id="paint0_linear_1131_5935" x1="236" y1="1" x2="236" y2="149" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#f0f2f5"></stop>
                        <stop offset="1" stop-color="#f0f2f5" stop-opacity="0"></stop>
                      </linearGradient>
                    </defs>
                  </svg>
                  <div class="flex justify-around">
                    <p class="text-[#60768a] text-[13px] font-bold leading-normal tracking-[0.015em]">Week 1</p>
                    <p class="text-[#60768a] text-[13px] font-bold leading-normal tracking-[0.015em]">Week 2</p>
                    <p class="text-[#60768a] text-[13px] font-bold leading-normal tracking-[0.015em]">Week 3</p>
                    <p class="text-[#60768a] text-[13px] font-bold leading-normal tracking-[0.015em]">Week 4</p>
                  </div>
                </div>
              </div>
              <div class="flex min-w-72 flex-1 flex-col gap-2 rounded-xl border border-[#dbe1e6] p-6">
                <p class="text-[#111518] text-base font-medium leading-normal">Conversion Rates by Campaign (vs. Previous Period)</p>
                <p class="text-[#111518] tracking-light text-[32px] font-bold leading-tight truncate">+8%</p>
                <div class="flex gap-1">
                  <p class="text-[#60768a] text-base font-normal leading-normal">Last 30 Days</p>
                  <p class="text-[#078838] text-base font-medium leading-normal">+8%</p>
                </div>
                <div class="grid min-h-[180px] grid-flow-col gap-6 grid-rows-[1fr_auto] items-end justify-items-center px-3">
                  <div class="border-[#60768a] bg-[#f0f2f5] border-t-2 w-full" style="height: 70%;"></div>
                  <p class="text-[#60768a] text-[13px] font-bold leading-normal tracking-[0.015em]">Campaign A</p>
                  <div class="border-[#60768a] bg-[#f0f2f5] border-t-2 w-full" style="height: 70%;"></div>
                  <p class="text-[#60768a] text-[13px] font-bold leading-normal tracking-[0.015em]">Campaign B</p>
                  <div class="border-[#60768a] bg-[#f0f2f5] border-t-2 w-full" style="height: 10%;"></div>
                  <p class="text-[#60768a] text-[13px] font-bold leading-normal tracking-[0.015em]">Campaign C</p>
                </div>
              </div>
            </div>
            <h3 class="text-[#111518] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Active Campaigns List</h3>
            <div class="px-4 py-3 @container">
              <div class="flex overflow-hidden rounded-xl border border-[#dbe1e6] bg-white">
                <table class="flex-1">
                  <thead>
                    <tr class="bg-white">
                      <th class="table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-120 px-4 py-3 text-left text-[#111518] w-[400px] text-sm font-medium leading-normal">Name</th>
                      <th class="table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-240 px-4 py-3 text-left text-[#111518] w-[400px] text-sm font-medium leading-normal">Product</th>
                      <th class="table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-360 px-4 py-3 text-left text-[#111518] w-[400px] text-sm font-medium leading-normal"># Leads</th>
                      <th class="table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-480 px-4 py-3 text-left text-[#111518] w-[400px] text-sm font-medium leading-normal"># Sent</th>
                      <th class="table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-600 px-4 py-3 text-left text-[#111518] w-[400px] text-sm font-medium leading-normal">
                        # Converted
                      </th>
                      <th class="table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-720 px-4 py-3 text-left text-[#111518] w-60 text-sm font-medium leading-normal">Status</th>
                      <th class="table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-840 px-4 py-3 text-left text-[#111518] w-60 text-[#60768a] text-sm font-medium leading-normal">
                        Action
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr class="border-t border-t-[#dbe1e6]">
                      <td class="table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-120 h-[72px] px-4 py-2 w-[400px] text-[#111518] text-sm font-normal leading-normal">
                        Summer Sale
                      </td>
                      <td class="table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-240 h-[72px] px-4 py-2 w-[400px] text-[#60768a] text-sm font-normal leading-normal">Fashion</td>
                      <td class="table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-360 h-[72px] px-4 py-2 w-[400px] text-[#60768a] text-sm font-normal leading-normal">1,200</td>
                      <td class="table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-480 h-[72px] px-4 py-2 w-[400px] text-[#60768a] text-sm font-normal leading-normal">3,500</td>
                      <td class="table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-600 h-[72px] px-4 py-2 w-[400px] text-[#60768a] text-sm font-normal leading-normal">250</td>
                      <td class="table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-720 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <button
                          class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#f0f2f5] text-[#111518] text-sm font-medium leading-normal w-full"
                        >
                          <span class="truncate">Active</span>
                        </button>
                      </td>
                      <td class="table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-840 h-[72px] px-4 py-2 w-60 text-[#60768a] text-sm font-bold leading-normal tracking-[0.015em]">
                        View
                      </td>
                    </tr>
                    <tr class="border-t border-t-[#dbe1e6]">
                      <td class="table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-120 h-[72px] px-4 py-2 w-[400px] text-[#111518] text-sm font-normal leading-normal">
                        Back to School
                      </td>
                      <td class="table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-240 h-[72px] px-4 py-2 w-[400px] text-[#60768a] text-sm font-normal leading-normal">
                        Education
                      </td>
                      <td class="table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-360 h-[72px] px-4 py-2 w-[400px] text-[#60768a] text-sm font-normal leading-normal">800</td>
                      <td class="table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-480 h-[72px] px-4 py-2 w-[400px] text-[#60768a] text-sm font-normal leading-normal">2,000</td>
                      <td class="table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-600 h-[72px] px-4 py-2 w-[400px] text-[#60768a] text-sm font-normal leading-normal">150</td>
                      <td class="table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-720 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <button
                          class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#f0f2f5] text-[#111518] text-sm font-medium leading-normal w-full"
                        >
                          <span class="truncate">Active</span>
                        </button>
                      </td>
                      <td class="table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-840 h-[72px] px-4 py-2 w-60 text-[#60768a] text-sm font-bold leading-normal tracking-[0.015em]">
                        View
                      </td>
                    </tr>
                    <tr class="border-t border-t-[#dbe1e6]">
                      <td class="table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-120 h-[72px] px-4 py-2 w-[400px] text-[#111518] text-sm font-normal leading-normal">
                        Holiday Promotion
                      </td>
                      <td class="table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-240 h-[72px] px-4 py-2 w-[400px] text-[#60768a] text-sm font-normal leading-normal">
                        Electronics
                      </td>
                      <td class="table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-360 h-[72px] px-4 py-2 w-[400px] text-[#60768a] text-sm font-normal leading-normal">1,500</td>
                      <td class="table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-480 h-[72px] px-4 py-2 w-[400px] text-[#60768a] text-sm font-normal leading-normal">4,000</td>
                      <td class="table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-600 h-[72px] px-4 py-2 w-[400px] text-[#60768a] text-sm font-normal leading-normal">300</td>
                      <td class="table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-720 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <button
                          class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#f0f2f5] text-[#111518] text-sm font-medium leading-normal w-full"
                        >
                          <span class="truncate">Paused</span>
                        </button>
                      </td>
                      <td class="table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-840 h-[72px] px-4 py-2 w-60 text-[#60768a] text-sm font-bold leading-normal tracking-[0.015em]">
                        View
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <style>
                          @container(max-width:120px){.table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-120{display: none;}}
                @container(max-width:240px){.table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-240{display: none;}}
                @container(max-width:360px){.table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-360{display: none;}}
                @container(max-width:480px){.table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-480{display: none;}}
                @container(max-width:600px){.table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-600{display: none;}}
                @container(max-width:720px){.table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-720{display: none;}}
                @container(max-width:840px){.table-79f4b95b-2ada-4073-8fc5-924d3075f77e-column-840{display: none;}}
              </style>
            </div>
            <div class="flex items-center justify-center p-4">
              <a href="#" class="flex size-10 items-center justify-center">
                <div class="text-[#111518]" data-icon="CaretLeft" data-size="18px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="18px" height="18px" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M165.66,202.34a8,8,0,0,1-11.32,11.32l-80-80a8,8,0,0,1,0-11.32l80-80a8,8,0,0,1,11.32,11.32L91.31,128Z"></path>
                  </svg>
                </div>
              </a>
              <a class="text-sm font-bold leading-normal tracking-[0.015em] flex size-10 items-center justify-center text-[#111518] rounded-full bg-[#f0f2f5]" href="#">1</a>
              <a class="text-sm font-normal leading-normal flex size-10 items-center justify-center text-[#111518] rounded-full" href="#">2</a>
              <a class="text-sm font-normal leading-normal flex size-10 items-center justify-center text-[#111518] rounded-full" href="#">3</a>
              <a href="#" class="flex size-10 items-center justify-center">
                <div class="text-[#111518]" data-icon="CaretRight" data-size="18px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="18px" height="18px" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M181.66,133.66l-80,80a8,8,0,0,1-11.32-11.32L164.69,128,90.34,53.66a8,8,0,0,1,11.32-11.32l80,80A8,8,0,0,1,181.66,133.66Z"></path>
                  </svg>
                </div>
              </a>
            </div>
            <h3 class="text-[#111518] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Recent Lead Activity</h3>
            <div class="px-4 py-3 @container">
              <div class="flex overflow-hidden rounded-xl border border-[#dbe1e6] bg-white">
                <table class="flex-1">
                  <thead>
                    <tr class="bg-white">
                      <th class="table-b8c14174-dd70-4c8c-9a0b-e5e53a4dba8b-column-120 px-4 py-3 text-left text-[#111518] w-[400px] text-sm font-medium leading-normal">
                        Lead Name
                      </th>
                      <th class="table-b8c14174-dd70-4c8c-9a0b-e5e53a4dba8b-column-240 px-4 py-3 text-left text-[#111518] w-[400px] text-sm font-medium leading-normal">Email</th>
                      <th class="table-b8c14174-dd70-4c8c-9a0b-e5e53a4dba8b-column-360 px-4 py-3 text-left text-[#111518] w-[400px] text-sm font-medium leading-normal">Source</th>
                      <th class="table-b8c14174-dd70-4c8c-9a0b-e5e53a4dba8b-column-480 px-4 py-3 text-left text-[#111518] w-[400px] text-sm font-medium leading-normal">
                        Date Added
                      </th>
                      <th class="table-b8c14174-dd70-4c8c-9a0b-e5e53a4dba8b-column-600 px-4 py-3 text-left text-[#111518] w-60 text-[#60768a] text-sm font-medium leading-normal">
                        Action
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr class="border-t border-t-[#dbe1e6]">
                      <td class="table-b8c14174-dd70-4c8c-9a0b-e5e53a4dba8b-column-120 h-[72px] px-4 py-2 w-[400px] text-[#111518] text-sm font-normal leading-normal">
                        Emma Bennett
                      </td>
                      <td class="table-b8c14174-dd70-4c8c-9a0b-e5e53a4dba8b-column-240 h-[72px] px-4 py-2 w-[400px] text-[#60768a] text-sm font-normal leading-normal">
                        <EMAIL>
                      </td>
                      <td class="table-b8c14174-dd70-4c8c-9a0b-e5e53a4dba8b-column-360 h-[72px] px-4 py-2 w-[400px] text-[#60768a] text-sm font-normal leading-normal">Website</td>
                      <td class="table-b8c14174-dd70-4c8c-9a0b-e5e53a4dba8b-column-480 h-[72px] px-4 py-2 w-[400px] text-[#60768a] text-sm font-normal leading-normal">
                        2023-11-15
                      </td>
                      <td class="table-b8c14174-dd70-4c8c-9a0b-e5e53a4dba8b-column-600 h-[72px] px-4 py-2 w-60 text-[#60768a] text-sm font-bold leading-normal tracking-[0.015em]">
                        View
                      </td>
                    </tr>
                    <tr class="border-t border-t-[#dbe1e6]">
                      <td class="table-b8c14174-dd70-4c8c-9a0b-e5e53a4dba8b-column-120 h-[72px] px-4 py-2 w-[400px] text-[#111518] text-sm font-normal leading-normal">
                        Noah Thompson
                      </td>
                      <td class="table-b8c14174-dd70-4c8c-9a0b-e5e53a4dba8b-column-240 h-[72px] px-4 py-2 w-[400px] text-[#60768a] text-sm font-normal leading-normal">
                        <EMAIL>
                      </td>
                      <td class="table-b8c14174-dd70-4c8c-9a0b-e5e53a4dba8b-column-360 h-[72px] px-4 py-2 w-[400px] text-[#60768a] text-sm font-normal leading-normal">Referral</td>
                      <td class="table-b8c14174-dd70-4c8c-9a0b-e5e53a4dba8b-column-480 h-[72px] px-4 py-2 w-[400px] text-[#60768a] text-sm font-normal leading-normal">
                        2023-11-14
                      </td>
                      <td class="table-b8c14174-dd70-4c8c-9a0b-e5e53a4dba8b-column-600 h-[72px] px-4 py-2 w-60 text-[#60768a] text-sm font-bold leading-normal tracking-[0.015em]">
                        View
                      </td>
                    </tr>
                    <tr class="border-t border-t-[#dbe1e6]">
                      <td class="table-b8c14174-dd70-4c8c-9a0b-e5e53a4dba8b-column-120 h-[72px] px-4 py-2 w-[400px] text-[#111518] text-sm font-normal leading-normal">
                        Ava Mitchell
                      </td>
                      <td class="table-b8c14174-dd70-4c8c-9a0b-e5e53a4dba8b-column-240 h-[72px] px-4 py-2 w-[400px] text-[#60768a] text-sm font-normal leading-normal">
                        <EMAIL>
                      </td>
                      <td class="table-b8c14174-dd70-4c8c-9a0b-e5e53a4dba8b-column-360 h-[72px] px-4 py-2 w-[400px] text-[#60768a] text-sm font-normal leading-normal">
                        Social Media
                      </td>
                      <td class="table-b8c14174-dd70-4c8c-9a0b-e5e53a4dba8b-column-480 h-[72px] px-4 py-2 w-[400px] text-[#60768a] text-sm font-normal leading-normal">
                        2023-11-13
                      </td>
                      <td class="table-b8c14174-dd70-4c8c-9a0b-e5e53a4dba8b-column-600 h-[72px] px-4 py-2 w-60 text-[#60768a] text-sm font-bold leading-normal tracking-[0.015em]">
                        View
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <style>
                          @container(max-width:120px){.table-b8c14174-dd70-4c8c-9a0b-e5e53a4dba8b-column-120{display: none;}}
                @container(max-width:240px){.table-b8c14174-dd70-4c8c-9a0b-e5e53a4dba8b-column-240{display: none;}}
                @container(max-width:360px){.table-b8c14174-dd70-4c8c-9a0b-e5e53a4dba8b-column-360{display: none;}}
                @container(max-width:480px){.table-b8c14174-dd70-4c8c-9a0b-e5e53a4dba8b-column-480{display: none;}}
                @container(max-width:600px){.table-b8c14174-dd70-4c8c-9a0b-e5e53a4dba8b-column-600{display: none;}}
              </style>
            </div>
            <div class="flex items-center justify-center p-4">
              <a href="#" class="flex size-10 items-center justify-center">
                <div class="text-[#111518]" data-icon="CaretLeft" data-size="18px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="18px" height="18px" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M165.66,202.34a8,8,0,0,1-11.32,11.32l-80-80a8,8,0,0,1,0-11.32l80-80a8,8,0,0,1,11.32,11.32L91.31,128Z"></path>
                  </svg>
                </div>
              </a>
              <a class="text-sm font-bold leading-normal tracking-[0.015em] flex size-10 items-center justify-center text-[#111518] rounded-full bg-[#f0f2f5]" href="#">1</a>
              <a class="text-sm font-normal leading-normal flex size-10 items-center justify-center text-[#111518] rounded-full" href="#">2</a>
              <a class="text-sm font-normal leading-normal flex size-10 items-center justify-center text-[#111518] rounded-full" href="#">3</a>
              <a href="#" class="flex size-10 items-center justify-center">
                <div class="text-[#111518]" data-icon="CaretRight" data-size="18px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="18px" height="18px" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M181.66,133.66l-80,80a8,8,0,0,1-11.32-11.32L164.69,128,90.34,53.66a8,8,0,0,1,11.32-11.32l80,80A8,8,0,0,1,181.66,133.66Z"></path>
                  </svg>
                </div>
              </a>
            </div>
            <h3 class="text-[#111518] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Recent Conversions</h3>
            <div class="px-4 py-3 @container">
              <div class="flex overflow-hidden rounded-xl border border-[#dbe1e6] bg-white">
                <table class="flex-1">
                  <thead>
                    <tr class="bg-white">
                      <th class="table-845c80ca-f2c6-48ea-82e2-02e37a452c8a-column-120 px-4 py-3 text-left text-[#111518] w-[400px] text-sm font-medium leading-normal">
                        Lead Name
                      </th>
                      <th class="table-845c80ca-f2c6-48ea-82e2-02e37a452c8a-column-240 px-4 py-3 text-left text-[#111518] w-[400px] text-sm font-medium leading-normal">
                        Campaign
                      </th>
                      <th class="table-845c80ca-f2c6-48ea-82e2-02e37a452c8a-column-360 px-4 py-3 text-left text-[#111518] w-[400px] text-sm font-medium leading-normal">
                        Conversion Date
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr class="border-t border-t-[#dbe1e6]">
                      <td class="table-845c80ca-f2c6-48ea-82e2-02e37a452c8a-column-120 h-[72px] px-4 py-2 w-[400px] text-[#111518] text-sm font-normal leading-normal">
                        Emma Bennett
                      </td>
                      <td class="table-845c80ca-f2c6-48ea-82e2-02e37a452c8a-column-240 h-[72px] px-4 py-2 w-[400px] text-[#60768a] text-sm font-normal leading-normal">
                        Summer Sale
                      </td>
                      <td class="table-845c80ca-f2c6-48ea-82e2-02e37a452c8a-column-360 h-[72px] px-4 py-2 w-[400px] text-[#60768a] text-sm font-normal leading-normal">
                        2023-11-16
                      </td>
                    </tr>
                    <tr class="border-t border-t-[#dbe1e6]">
                      <td class="table-845c80ca-f2c6-48ea-82e2-02e37a452c8a-column-120 h-[72px] px-4 py-2 w-[400px] text-[#111518] text-sm font-normal leading-normal">
                        Noah Thompson
                      </td>
                      <td class="table-845c80ca-f2c6-48ea-82e2-02e37a452c8a-column-240 h-[72px] px-4 py-2 w-[400px] text-[#60768a] text-sm font-normal leading-normal">
                        Back to School
                      </td>
                      <td class="table-845c80ca-f2c6-48ea-82e2-02e37a452c8a-column-360 h-[72px] px-4 py-2 w-[400px] text-[#60768a] text-sm font-normal leading-normal">
                        2023-11-15
                      </td>
                    </tr>
                    <tr class="border-t border-t-[#dbe1e6]">
                      <td class="table-845c80ca-f2c6-48ea-82e2-02e37a452c8a-column-120 h-[72px] px-4 py-2 w-[400px] text-[#111518] text-sm font-normal leading-normal">
                        Ava Mitchell
                      </td>
                      <td class="table-845c80ca-f2c6-48ea-82e2-02e37a452c8a-column-240 h-[72px] px-4 py-2 w-[400px] text-[#60768a] text-sm font-normal leading-normal">
                        Summer Sale
                      </td>
                      <td class="table-845c80ca-f2c6-48ea-82e2-02e37a452c8a-column-360 h-[72px] px-4 py-2 w-[400px] text-[#60768a] text-sm font-normal leading-normal">
                        2023-11-14
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <style>
                          @container(max-width:120px){.table-845c80ca-f2c6-48ea-82e2-02e37a452c8a-column-120{display: none;}}
                @container(max-width:240px){.table-845c80ca-f2c6-48ea-82e2-02e37a452c8a-column-240{display: none;}}
                @container(max-width:360px){.table-845c80ca-f2c6-48ea-82e2-02e37a452c8a-column-360{display: none;}}
              </style>
            </div>
            <div class="flex items-center justify-center p-4">
              <a href="#" class="flex size-10 items-center justify-center">
                <div class="text-[#111518]" data-icon="CaretLeft" data-size="18px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="18px" height="18px" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M165.66,202.34a8,8,0,0,1-11.32,11.32l-80-80a8,8,0,0,1,0-11.32l80-80a8,8,0,0,1,11.32,11.32L91.31,128Z"></path>
                  </svg>
                </div>
              </a>
              <a class="text-sm font-bold leading-normal tracking-[0.015em] flex size-10 items-center justify-center text-[#111518] rounded-full bg-[#f0f2f5]" href="#">1</a>
              <a class="text-sm font-normal leading-normal flex size-10 items-center justify-center text-[#111518] rounded-full" href="#">2</a>
              <a class="text-sm font-normal leading-normal flex size-10 items-center justify-center text-[#111518] rounded-full" href="#">3</a>
              <a href="#" class="flex size-10 items-center justify-center">
                <div class="text-[#111518]" data-icon="CaretRight" data-size="18px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="18px" height="18px" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M181.66,133.66l-80,80a8,8,0,0,1-11.32-11.32L164.69,128,90.34,53.66a8,8,0,0,1,11.32-11.32l80,80A8,8,0,0,1,181.66,133.66Z"></path>
                  </svg>
                </div>
              </a>
            </div>
          </div>
          <div class="layout-content-container flex flex-col w-[360px]">
            <h3 class="text-[#111518] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Quick Actions</h3>
            <div class="flex justify-center">
              <div class="flex flex-1 gap-3 max-w-[480px] flex-col items-stretch px-4 py-3">
                <button
                  class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#0b80ee] text-white text-sm font-bold leading-normal tracking-[0.015em] w-full"
                >
                  <span class="truncate">Add New Lead</span>
                </button>
                <button
                  class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#0b80ee] text-white text-sm font-bold leading-normal tracking-[0.015em] w-full"
                >
                  <span class="truncate">Create New Campaign</span>
                </button>
                <button
                  class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#0b80ee] text-white text-sm font-bold leading-normal tracking-[0.015em] w-full"
                >
                  <span class="truncate">Add New Product</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>


    {% endblock %}
